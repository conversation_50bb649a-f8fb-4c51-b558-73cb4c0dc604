<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin: 10px 0; }
        input { padding: 10px; width: 300px; margin: 5px 0; }
        button { padding: 10px 20px; background: #ff6b35; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Test User Authentication</h1>
    
    <h2>Sign Up</h2>
    <form id="testSignupForm">
        <div class="form-group">
            <input type="text" id="testName" placeholder="Full Name" required>
        </div>
        <div class="form-group">
            <input type="email" id="testEmail" placeholder="Email" required>
        </div>
        <div class="form-group">
            <input type="tel" id="testPhone" placeholder="Phone Number" required>
        </div>
        <div class="form-group">
            <input type="password" id="testPassword" placeholder="Password" required>
        </div>
        <button type="submit">Sign Up</button>
    </form>
    
    <h2>Login</h2>
    <form id="testLoginForm">
        <div class="form-group">
            <input type="email" id="testLoginEmail" placeholder="Email" required>
        </div>
        <div class="form-group">
            <input type="password" id="testLoginPassword" placeholder="Password" required>
        </div>
        <button type="submit">Login</button>
    </form>
    
    <div id="result" class="result"></div>

    <script>
        const API_BASE = '/api';
        
        document.getElementById('testSignupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('testName').value;
            const email = document.getElementById('testEmail').value;
            const phone = document.getElementById('testPhone').value;
            const password = document.getElementById('testPassword').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, email, phone, password })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>Signup Result:</h3>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        });
        
        document.getElementById('testLoginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('testLoginEmail').value;
            const password = document.getElementById('testLoginPassword').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>Login Result:</h3>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
