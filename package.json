{"name": "food-ordering", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node backend/server.js", "seed": "node backend/seedData.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.1"}}