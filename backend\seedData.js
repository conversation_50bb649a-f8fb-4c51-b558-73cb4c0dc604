const mongoose = require('mongoose');
const MenuItem = require('./models/MenuItem');
require('dotenv').config({ path: __dirname + '/.env' });

const sampleMenuItems = [
  {
    name: "Margherita Pizza",
    description: "Classic pizza with fresh tomatoes, mozzarella cheese, and basil",
    price: 12.99,
    category: "pizza",
    image: "https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300&h=200&fit=crop",
    ingredients: ["tomato sauce", "mozzarella", "basil", "olive oil"],
    isVegetarian: true,
    preparationTime: 15
  },
  {
    name: "Chicken Burger",
    description: "Juicy grilled chicken breast with lettuce, tomato, and mayo",
    price: 9.99,
    category: "burgers",
    image: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop",
    ingredients: ["chicken breast", "lettuce", "tomato", "mayo", "bun"],
    preparationTime: 20
  },
  {
    name: "Chicken Tikka Masala",
    description: "Tender chicken in a creamy tomato-based curry sauce",
    price: 14.99,
    category: "indian",
    image: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=300&h=200&fit=crop",
    ingredients: ["chicken", "tomatoes", "cream", "spices"],
    spiceLevel: "medium",
    preparationTime: 25
  },
  {
    name: "Kung Pao Chicken",
    description: "Spicy stir-fried chicken with peanuts and vegetables",
    price: 13.99,
    category: "chinese",
    image: "https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?w=300&h=200&fit=crop",
    ingredients: ["chicken", "peanuts", "vegetables", "chili"],
    spiceLevel: "hot",
    preparationTime: 18
  },
  {
    name: "Spaghetti Carbonara",
    description: "Classic Italian pasta with eggs, cheese, and pancetta",
    price: 11.99,
    category: "italian",
    image: "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=300&h=200&fit=crop",
    ingredients: ["spaghetti", "eggs", "parmesan", "pancetta"],
    preparationTime: 20
  },
  {
    name: "Caesar Salad",
    description: "Fresh romaine lettuce with Caesar dressing and croutons",
    price: 8.99,
    category: "appetizers",
    image: "https://images.unsplash.com/photo-1546793665-c74683f339c1?w=300&h=200&fit=crop",
    ingredients: ["romaine lettuce", "caesar dressing", "croutons", "parmesan"],
    isVegetarian: true,
    preparationTime: 10
  },
  {
    name: "Chocolate Brownie",
    description: "Rich chocolate brownie served with vanilla ice cream",
    price: 6.99,
    category: "desserts",
    image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=300&h=200&fit=crop",
    ingredients: ["chocolate", "flour", "eggs", "vanilla ice cream"],
    isVegetarian: true,
    preparationTime: 5
  },
  {
    name: "Fresh Orange Juice",
    description: "Freshly squeezed orange juice",
    price: 3.99,
    category: "beverages",
    image: "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300&h=200&fit=crop",
    ingredients: ["fresh oranges"],
    isVegetarian: true,
    isVegan: true,
    preparationTime: 5
  },
  {
    name: "Pepperoni Pizza",
    description: "Classic pizza with pepperoni and mozzarella cheese",
    price: 14.99,
    category: "pizza",
    image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop",
    ingredients: ["tomato sauce", "mozzarella", "pepperoni"],
    preparationTime: 15
  },
  {
    name: "Veggie Burger",
    description: "Plant-based burger with fresh vegetables",
    price: 10.99,
    category: "burgers",
    image: "https://images.unsplash.com/photo-1520072959219-c595dc870360?w=300&h=200&fit=crop",
    ingredients: ["veggie patty", "lettuce", "tomato", "onion", "bun"],
    isVegetarian: true,
    isVegan: true,
    preparationTime: 18
  }
];

async function seedDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Clear existing menu items
    await MenuItem.deleteMany({});
    console.log('Cleared existing menu items');

    // Insert sample data
    await MenuItem.insertMany(sampleMenuItems);
    console.log('Sample menu items inserted successfully');

    mongoose.connection.close();
    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
