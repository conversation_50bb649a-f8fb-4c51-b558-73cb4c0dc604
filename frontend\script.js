const form = document.getElementById('orderForm');
const ordersDiv = document.getElementById('orders');

form.addEventListener('submit', async (e) => {
  e.preventDefault();

  const data = {
    customerName: document.getElementById('customerName').value,
    foodItem: document.getElementById('foodItem').value,
    quantity: +document.getElementById('quantity').value,
    address: document.getElementById('address').value
  };

  const res = await fetch('http://localhost:3000/api/orders', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });

  const newOrder = await res.json();
  displayOrders();
  form.reset();
});

async function displayOrders() {
  const res = await fetch('http://localhost:3000/api/orders');
  const orders = await res.json();

  ordersDiv.innerHTML = '<h2>All Orders</h2>' + orders.map(order => `
    <div>
      <strong>${order.customerName}</strong> ordered ${order.quantity} x ${order.foodItem}
      <br>Status: ${order.status}
      <hr>
    </div>
  `).join('');
}

displayOrders();
