// Global Variables
let currentUser = null;
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let menuItems = [];

// API Base URL
const API_BASE = '/api';

// DOM Elements
const elements = {
  // Navigation
  hamburger: document.getElementById('hamburger'),
  navMenu: document.querySelector('.nav-menu'),
  cartIcon: document.getElementById('cartIcon'),
  cartCount: document.getElementById('cartCount'),
  authButtons: document.getElementById('authButtons'),
  userMenu: document.getElementById('userMenu'),
  userAvatar: document.getElementById('userAvatar'),
  dropdownMenu: document.getElementById('dropdownMenu'),

  // Buttons
  loginBtn: document.getElementById('loginBtn'),
  signupBtn: document.getElementById('signupBtn'),
  logoutBtn: document.getElementById('logoutBtn'),

  // Modals
  loginModal: document.getElementById('loginModal'),
  signupModal: document.getElementById('signupModal'),
  cartModal: document.getElementById('cartModal'),
  checkoutModal: document.getElementById('checkoutModal'),

  // Forms
  loginForm: document.getElementById('loginForm'),
  signupForm: document.getElementById('signupForm'),
  checkoutForm: document.getElementById('checkoutForm'),

  // Menu
  menuGrid: document.getElementById('menuGrid'),
  searchInput: document.getElementById('searchInput'),
  filterButtons: document.querySelectorAll('.filter-btn'),

  // Cart
  cartItems: document.getElementById('cartItems'),
  cartTotal: document.getElementById('cartTotal'),
  checkoutBtn: document.getElementById('checkoutBtn'),

  // Checkout
  checkoutItems: document.getElementById('checkoutItems'),
  checkoutTotal: document.getElementById('checkoutTotal'),

  // Notifications
  notification: document.getElementById('notification'),
  notificationMessage: document.getElementById('notificationMessage'),
  loadingSpinner: document.getElementById('loadingSpinner')
};

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
  console.log('FoodieHub app initialized');
  console.log('Elements found:', {
    loginForm: !!elements.loginForm,
    signupForm: !!elements.signupForm,
    loginBtn: !!elements.loginBtn,
    signupBtn: !!elements.signupBtn
  });

  initializeApp();
  setupEventListeners();
  checkAuthStatus();
  loadMenuItems();
  updateCartUI();
});

// Initialize Application
function initializeApp() {
  // Set up navigation active states
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      navLinks.forEach(l => l.classList.remove('active'));
      this.classList.add('active');

      const target = this.getAttribute('href');
      if (target.startsWith('#')) {
        document.querySelector(target).scrollIntoView({
          behavior: 'smooth'
        });
      }
    });
  });
}

// Setup Event Listeners
function setupEventListeners() {
  // Mobile menu toggle
  elements.hamburger?.addEventListener('click', toggleMobileMenu);

  // Auth buttons
  elements.loginBtn?.addEventListener('click', () => {
    console.log('Login button clicked');
    showModal('loginModal');
  });
  elements.signupBtn?.addEventListener('click', () => {
    console.log('Signup button clicked');
    showModal('signupModal');
  });
  elements.logoutBtn?.addEventListener('click', logout);

  // User menu dropdown
  elements.userAvatar?.addEventListener('click', toggleUserDropdown);

  // Cart
  elements.cartIcon?.addEventListener('click', () => showModal('cartModal'));
  elements.checkoutBtn?.addEventListener('click', () => {
    if (cart.length === 0) {
      showNotification('Your cart is empty!', 'warning');
      return;
    }
    if (!currentUser) {
      showNotification('Please login to checkout', 'warning');
      showModal('loginModal');
      return;
    }
    hideModal('cartModal');
    showModal('checkoutModal');
    populateCheckoutForm();
  });

  // Forms
  if (elements.loginForm) {
    console.log('Login form found, adding event listener');
    elements.loginForm.addEventListener('submit', handleLogin);
  } else {
    console.log('Login form not found');
  }

  if (elements.signupForm) {
    console.log('Signup form found, adding event listener');
    elements.signupForm.addEventListener('submit', handleSignup);
  } else {
    console.log('Signup form not found');
  }

  elements.checkoutForm?.addEventListener('submit', handleCheckout);

  // Search and filter
  elements.searchInput?.addEventListener('input', filterMenuItems);
  elements.filterButtons?.forEach(btn => {
    btn.addEventListener('click', function() {
      elements.filterButtons.forEach(b => b.classList.remove('active'));
      this.classList.add('active');
      filterMenuItems();
    });
  });

  // Modal close buttons
  document.querySelectorAll('.close').forEach(closeBtn => {
    closeBtn.addEventListener('click', function() {
      const modal = this.closest('.modal');
      hideModal(modal.id);
    });
  });

  // Close modals when clicking outside
  document.querySelectorAll('.modal').forEach(modal => {
    modal.addEventListener('click', function(e) {
      if (e.target === this) {
        hideModal(this.id);
      }
    });
  });

  // Modal switching
  document.getElementById('switchToSignup')?.addEventListener('click', function(e) {
    e.preventDefault();
    hideModal('loginModal');
    showModal('signupModal');
  });

  document.getElementById('switchToLogin')?.addEventListener('click', function(e) {
    e.preventDefault();
    hideModal('signupModal');
    showModal('loginModal');
  });

  // Notification close
  document.getElementById('closeNotification')?.addEventListener('click', hideNotification);
}

// Authentication Functions
async function checkAuthStatus() {
  const token = localStorage.getItem('token');
  if (!token) {
    updateAuthUI(false);
    return;
  }

  try {
    const response = await fetch(`${API_BASE}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      currentUser = data.user;
      updateAuthUI(true);
    } else {
      localStorage.removeItem('token');
      updateAuthUI(false);
    }
  } catch (error) {
    console.error('Auth check failed:', error);
    localStorage.removeItem('token');
    updateAuthUI(false);
  }
}

async function handleLogin(e) {
  e.preventDefault();
  console.log('Login form submitted');
  showLoading(true);

  const email = document.getElementById('loginEmail').value;
  const password = document.getElementById('loginPassword').value;

  console.log('Login data:', { email, password: '***' });

  try {
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    const data = await response.json();

    if (response.ok) {
      localStorage.setItem('token', data.token);
      currentUser = data.user;
      updateAuthUI(true);
      hideModal('loginModal');
      showNotification('Login successful!', 'success');
      elements.loginForm.reset();
    } else {
      showNotification(data.message || 'Login failed', 'error');
    }
  } catch (error) {
    console.error('Login error:', error);
    showNotification('Login failed. Please try again.', 'error');
  } finally {
    showLoading(false);
  }
}

async function handleSignup(e) {
  e.preventDefault();
  console.log('Signup form submitted');
  showLoading(true);

  const name = document.getElementById('signupName').value;
  const email = document.getElementById('signupEmail').value;
  const phone = document.getElementById('signupPhone').value;
  const password = document.getElementById('signupPassword').value;
  const confirmPassword = document.getElementById('confirmPassword').value;

  console.log('Form data:', { name, email, phone, password: '***' });

  if (password !== confirmPassword) {
    showNotification('Passwords do not match', 'error');
    showLoading(false);
    return;
  }

  try {
    console.log('Sending registration request...');
    const response = await fetch(`${API_BASE}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name, email, phone, password })
    });

    console.log('Response status:', response.status);
    const data = await response.json();
    console.log('Response data:', data);

    if (response.ok) {
      localStorage.setItem('token', data.token);
      currentUser = data.user;
      updateAuthUI(true);
      hideModal('signupModal');
      showNotification('Account created successfully!', 'success');
      elements.signupForm.reset();
    } else {
      showNotification(data.message || 'Signup failed', 'error');
    }
  } catch (error) {
    console.error('Signup error:', error);
    showNotification('Signup failed. Please try again.', 'error');
  } finally {
    showLoading(false);
  }
}

function logout() {
  localStorage.removeItem('token');
  currentUser = null;
  cart = [];
  localStorage.removeItem('cart');
  updateAuthUI(false);
  updateCartUI();
  showNotification('Logged out successfully', 'success');
  hideUserDropdown();
}

function updateAuthUI(isLoggedIn) {
  if (isLoggedIn && currentUser) {
    elements.authButtons.style.display = 'none';
    elements.userMenu.style.display = 'block';
    // You could add user name or avatar here
  } else {
    elements.authButtons.style.display = 'flex';
    elements.userMenu.style.display = 'none';
  }
}

// Menu Functions
async function loadMenuItems() {
  showLoading(true);
  try {
    const response = await fetch(`${API_BASE}/menu`);
    const data = await response.json();

    if (response.ok) {
      menuItems = data.data;
      displayMenuItems(menuItems);
    } else {
      showNotification('Failed to load menu items', 'error');
    }
  } catch (error) {
    console.error('Error loading menu:', error);
    showNotification('Failed to load menu items', 'error');
  } finally {
    showLoading(false);
  }
}

function displayMenuItems(items) {
  if (!elements.menuGrid) return;

  elements.menuGrid.innerHTML = '';

  if (items.length === 0) {
    elements.menuGrid.innerHTML = '<p class="text-center">No items found</p>';
    return;
  }

  items.forEach(item => {
    const menuItemElement = createMenuItemElement(item);
    elements.menuGrid.appendChild(menuItemElement);
  });
}

function createMenuItemElement(item) {
  const div = document.createElement('div');
  div.className = 'menu-item';
  div.innerHTML = `
    <div class="menu-item-image" style="background-image: url('${item.image}')">
      ${item.isVegetarian ? '<div class="menu-item-badge">Vegetarian</div>' : ''}
    </div>
    <div class="menu-item-content">
      <h3>${item.name}</h3>
      <p>${item.description}</p>
      <div class="menu-item-footer">
        <span class="menu-item-price">$${item.price.toFixed(2)}</span>
        <button class="add-to-cart" onclick="addToCart('${item._id}')">
          Add to Cart
        </button>
      </div>
    </div>
  `;
  return div;
}

function filterMenuItems() {
  const searchTerm = elements.searchInput?.value.toLowerCase() || '';
  const activeCategory = document.querySelector('.filter-btn.active')?.dataset.category || 'all';

  let filteredItems = menuItems;

  // Filter by category
  if (activeCategory !== 'all') {
    filteredItems = filteredItems.filter(item => item.category === activeCategory);
  }

  // Filter by search term
  if (searchTerm) {
    filteredItems = filteredItems.filter(item =>
      item.name.toLowerCase().includes(searchTerm) ||
      item.description.toLowerCase().includes(searchTerm)
    );
  }

  displayMenuItems(filteredItems);
}

// Cart Functions
function addToCart(itemId) {
  const item = menuItems.find(item => item._id === itemId);
  if (!item) return;

  const existingItem = cart.find(cartItem => cartItem._id === itemId);

  if (existingItem) {
    existingItem.quantity += 1;
  } else {
    cart.push({
      _id: item._id,
      name: item.name,
      price: item.price,
      image: item.image,
      quantity: 1
    });
  }

  localStorage.setItem('cart', JSON.stringify(cart));
  updateCartUI();
  showNotification(`${item.name} added to cart!`, 'success');
}

function removeFromCart(itemId) {
  cart = cart.filter(item => item._id !== itemId);
  localStorage.setItem('cart', JSON.stringify(cart));
  updateCartUI();
  displayCartItems();
}

function updateCartQuantity(itemId, newQuantity) {
  if (newQuantity <= 0) {
    removeFromCart(itemId);
    return;
  }

  const item = cart.find(item => item._id === itemId);
  if (item) {
    item.quantity = newQuantity;
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartUI();
    displayCartItems();
  }
}

function updateCartUI() {
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  if (elements.cartCount) {
    elements.cartCount.textContent = totalItems;
  }

  if (elements.cartTotal) {
    elements.cartTotal.textContent = totalPrice.toFixed(2);
  }

  if (elements.checkoutTotal) {
    elements.checkoutTotal.textContent = totalPrice.toFixed(2);
  }
}

function displayCartItems() {
  if (!elements.cartItems) return;

  if (cart.length === 0) {
    elements.cartItems.innerHTML = `
      <div class="empty-cart">
        <i class="fas fa-shopping-cart"></i>
        <p>Your cart is empty</p>
      </div>
    `;
    return;
  }

  elements.cartItems.innerHTML = cart.map(item => `
    <div class="cart-item">
      <div class="cart-item-image" style="background-image: url('${item.image}')"></div>
      <div class="cart-item-details">
        <div class="cart-item-name">${item.name}</div>
        <div class="cart-item-price">$${item.price.toFixed(2)}</div>
      </div>
      <div class="quantity-controls">
        <button class="quantity-btn" onclick="updateCartQuantity('${item._id}', ${item.quantity - 1})">-</button>
        <span class="quantity">${item.quantity}</span>
        <button class="quantity-btn" onclick="updateCartQuantity('${item._id}', ${item.quantity + 1})">+</button>
      </div>
      <button class="remove-item" onclick="removeFromCart('${item._id}')">Remove</button>
    </div>
  `).join('');
}

// Checkout Functions
function populateCheckoutForm() {
  displayCartItems();

  if (elements.checkoutItems) {
    elements.checkoutItems.innerHTML = cart.map(item => `
      <div class="checkout-item">
        <span>${item.name} x ${item.quantity}</span>
        <span>$${(item.price * item.quantity).toFixed(2)}</span>
      </div>
    `).join('');
  }

  // Pre-fill user address if available
  if (currentUser && currentUser.address) {
    const address = currentUser.address;
    if (document.getElementById('deliveryStreet')) {
      document.getElementById('deliveryStreet').value = address.street || '';
    }
    if (document.getElementById('deliveryCity')) {
      document.getElementById('deliveryCity').value = address.city || '';
    }
    if (document.getElementById('deliveryState')) {
      document.getElementById('deliveryState').value = address.state || '';
    }
    if (document.getElementById('deliveryZip')) {
      document.getElementById('deliveryZip').value = address.zipCode || '';
    }
  }

  if (currentUser && document.getElementById('deliveryPhone')) {
    document.getElementById('deliveryPhone').value = currentUser.phone || '';
  }
}

async function handleCheckout(e) {
  e.preventDefault();
  showLoading(true);

  const deliveryAddress = {
    street: document.getElementById('deliveryStreet').value,
    city: document.getElementById('deliveryCity').value,
    state: document.getElementById('deliveryState').value,
    zipCode: document.getElementById('deliveryZip').value,
    phone: document.getElementById('deliveryPhone').value
  };

  const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
  const notes = document.getElementById('orderNotes').value;

  const orderData = {
    items: cart.map(item => ({
      menuItem: item._id,
      quantity: item.quantity,
      specialInstructions: ''
    })),
    deliveryAddress,
    paymentMethod,
    notes
  };

  try {
    const response = await fetch(`${API_BASE}/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(orderData)
    });

    const data = await response.json();

    if (response.ok) {
      cart = [];
      localStorage.removeItem('cart');
      updateCartUI();
      hideModal('checkoutModal');
      showNotification('Order placed successfully!', 'success');
      elements.checkoutForm.reset();
    } else {
      showNotification(data.message || 'Order failed', 'error');
    }
  } catch (error) {
    console.error('Checkout error:', error);
    showNotification('Order failed. Please try again.', 'error');
  } finally {
    showLoading(false);
  }
}

// UI Helper Functions
function showModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';

    // Special handling for cart modal
    if (modalId === 'cartModal') {
      displayCartItems();
    }
  }
}

function hideModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
  }
}

function showNotification(message, type = 'success') {
  if (!elements.notification || !elements.notificationMessage) return;

  elements.notificationMessage.textContent = message;
  elements.notification.className = `notification ${type} show`;

  // Auto hide after 5 seconds
  setTimeout(() => {
    hideNotification();
  }, 5000);
}

function hideNotification() {
  if (elements.notification) {
    elements.notification.classList.remove('show');
  }
}

function showLoading(show) {
  if (elements.loadingSpinner) {
    if (show) {
      elements.loadingSpinner.classList.add('show');
    } else {
      elements.loadingSpinner.classList.remove('show');
    }
  }
}

function toggleMobileMenu() {
  if (elements.hamburger && elements.navMenu) {
    elements.hamburger.classList.toggle('active');
    elements.navMenu.classList.toggle('active');
  }
}

function toggleUserDropdown() {
  if (elements.dropdownMenu) {
    elements.dropdownMenu.classList.toggle('show');
  }
}

function hideUserDropdown() {
  if (elements.dropdownMenu) {
    elements.dropdownMenu.classList.remove('show');
  }
}

// Utility Functions
function scrollToMenu() {
  document.getElementById('menu')?.scrollIntoView({
    behavior: 'smooth'
  });
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(e) {
  if (!elements.userMenu?.contains(e.target)) {
    hideUserDropdown();
  }

  if (!elements.navMenu?.contains(e.target) && !elements.hamburger?.contains(e.target)) {
    if (elements.navMenu?.classList.contains('active')) {
      toggleMobileMenu();
    }
  }
});

// Handle window resize
window.addEventListener('resize', function() {
  if (window.innerWidth > 768) {
    elements.navMenu?.classList.remove('active');
    elements.hamburger?.classList.remove('active');
  }
});

// Contact form handler
document.getElementById('contactForm')?.addEventListener('submit', function(e) {
  e.preventDefault();
  showNotification('Thank you for your message! We\'ll get back to you soon.', 'success');
  this.reset();
});

// Newsletter subscription
document.querySelector('.newsletter button')?.addEventListener('click', function(e) {
  e.preventDefault();
  const email = this.previousElementSibling.value;
  if (email) {
    showNotification('Thank you for subscribing to our newsletter!', 'success');
    this.previousElementSibling.value = '';
  }
});
