<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FoodieHub - Delicious Food Delivered</title>
  <link rel="stylesheet" href="style.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Navigation Bar -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-logo">
        <i class="fas fa-utensils"></i>
        <span>FoodieHub</span>
      </div>

      <ul class="nav-menu">
        <li class="nav-item">
          <a href="#home" class="nav-link active">Home</a>
        </li>
        <li class="nav-item">
          <a href="#menu" class="nav-link">Menu</a>
        </li>
        <li class="nav-item">
          <a href="#about" class="nav-link">About</a>
        </li>
        <li class="nav-item">
          <a href="#contact" class="nav-link">Contact</a>
        </li>
      </ul>

      <div class="nav-actions">
        <div class="cart-icon" id="cartIcon">
          <i class="fas fa-shopping-cart"></i>
          <span class="cart-count" id="cartCount">0</span>
        </div>
        <div class="auth-buttons" id="authButtons">
          <button class="btn-login" id="loginBtn">Login</button>
          <button class="btn-signup" id="signupBtn">Sign Up</button>
        </div>
        <div class="user-menu" id="userMenu" style="display: none;">
          <div class="user-avatar" id="userAvatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="dropdown-menu" id="dropdownMenu">
            <a href="#profile"><i class="fas fa-user"></i> Profile</a>
            <a href="#orders"><i class="fas fa-receipt"></i> My Orders</a>
            <a href="#" id="logoutBtn"><i class="fas fa-sign-out-alt"></i> Logout</a>
          </div>
        </div>
      </div>

      <div class="hamburger" id="hamburger">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main>
    <!-- Home Section -->
    <section id="home" class="hero">
      <div class="hero-content">
        <h1>Delicious Food <span class="highlight">Delivered</span> to Your Door</h1>
        <p>Experience the finest cuisine from the comfort of your home. Fresh ingredients, expert chefs, and lightning-fast delivery.</p>
        <div class="hero-buttons">
          <button class="btn-primary" onclick="scrollToMenu()">Order Now</button>
          <button class="btn-secondary">Learn More</button>
        </div>
      </div>
      <div class="hero-image">
        <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=600&h=400&fit=crop" alt="Delicious Food">
      </div>
    </section>

    <!-- Menu Section -->
    <section id="menu" class="menu-section">
      <div class="container">
        <h2 class="section-title">Our <span class="highlight">Menu</span></h2>
        <p class="section-subtitle">Discover our delicious selection of carefully crafted dishes</p>

        <!-- Search and Filter -->
        <div class="menu-controls">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="searchInput" placeholder="Search for dishes...">
          </div>
          <div class="filter-buttons">
            <button class="filter-btn active" data-category="all">All</button>
            <button class="filter-btn" data-category="pizza">Pizza</button>
            <button class="filter-btn" data-category="burgers">Burgers</button>
            <button class="filter-btn" data-category="indian">Indian</button>
            <button class="filter-btn" data-category="chinese">Chinese</button>
            <button class="filter-btn" data-category="italian">Italian</button>
            <button class="filter-btn" data-category="desserts">Desserts</button>
          </div>
        </div>

        <!-- Menu Items Grid -->
        <div class="menu-grid" id="menuGrid">
          <!-- Menu items will be loaded here dynamically -->
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
      <div class="container">
        <div class="about-content">
          <div class="about-text">
            <h2>About <span class="highlight">FoodieHub</span></h2>
            <p>We're passionate about bringing you the finest culinary experiences right to your doorstep. Our team of expert chefs uses only the freshest ingredients to create memorable meals that satisfy your cravings.</p>
            <div class="features">
              <div class="feature">
                <i class="fas fa-clock"></i>
                <h3>Fast Delivery</h3>
                <p>30-45 minutes guaranteed delivery time</p>
              </div>
              <div class="feature">
                <i class="fas fa-leaf"></i>
                <h3>Fresh Ingredients</h3>
                <p>Locally sourced, organic ingredients</p>
              </div>
              <div class="feature">
                <i class="fas fa-star"></i>
                <h3>Quality Assured</h3>
                <p>5-star rated chefs and recipes</p>
              </div>
            </div>
          </div>
          <div class="about-image">
            <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=500&h=400&fit=crop" alt="Our Kitchen">
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
      <div class="container">
        <h2 class="section-title">Get in <span class="highlight">Touch</span></h2>
        <div class="contact-content">
          <div class="contact-info">
            <div class="contact-item">
              <i class="fas fa-map-marker-alt"></i>
              <div>
                <h3>Address</h3>
                <p>123 Food Street, Culinary City, FC 12345</p>
              </div>
            </div>
            <div class="contact-item">
              <i class="fas fa-phone"></i>
              <div>
                <h3>Phone</h3>
                <p>+****************</p>
              </div>
            </div>
            <div class="contact-item">
              <i class="fas fa-envelope"></i>
              <div>
                <h3>Email</h3>
                <p><EMAIL></p>
              </div>
            </div>
          </div>
          <div class="contact-form">
            <form id="contactForm">
              <input type="text" placeholder="Your Name" required>
              <input type="email" placeholder="Your Email" required>
              <textarea placeholder="Your Message" rows="5" required></textarea>
              <button type="submit" class="btn-primary">Send Message</button>
            </form>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-logo">
            <i class="fas fa-utensils"></i>
            <span>FoodieHub</span>
          </div>
          <p>Delivering happiness, one meal at a time.</p>
          <div class="social-links">
            <a href="#"><i class="fab fa-facebook"></i></a>
            <a href="#"><i class="fab fa-twitter"></i></a>
            <a href="#"><i class="fab fa-instagram"></i></a>
            <a href="#"><i class="fab fa-youtube"></i></a>
          </div>
        </div>
        <div class="footer-section">
          <h3>Quick Links</h3>
          <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#menu">Menu</a></li>
            <li><a href="#about">About</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>Customer Service</h3>
          <ul>
            <li><a href="#">Help Center</a></li>
            <li><a href="#">Track Order</a></li>
            <li><a href="#">Returns</a></li>
            <li><a href="#">Privacy Policy</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>Newsletter</h3>
          <p>Subscribe to get updates on new dishes and offers</p>
          <div class="newsletter">
            <input type="email" placeholder="Enter your email">
            <button type="submit"><i class="fas fa-paper-plane"></i></button>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 FoodieHub. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Login Modal -->
  <div id="loginModal" class="modal">
    <div class="modal-content">
      <span class="close" id="closeLogin">&times;</span>
      <h2>Login to Your Account</h2>
      <form id="loginForm">
        <div class="form-group">
          <input type="email" id="loginEmail" placeholder="Email" required>
        </div>
        <div class="form-group">
          <input type="password" id="loginPassword" placeholder="Password" required>
        </div>
        <button type="submit" class="btn-primary full-width">Login</button>
      </form>
      <p class="modal-footer">Don't have an account? <a href="#" id="switchToSignup">Sign up here</a></p>
    </div>
  </div>

  <!-- Signup Modal -->
  <div id="signupModal" class="modal">
    <div class="modal-content">
      <span class="close" id="closeSignup">&times;</span>
      <h2>Create Your Account</h2>
      <form id="signupForm">
        <div class="form-group">
          <input type="text" id="signupName" placeholder="Full Name" required>
        </div>
        <div class="form-group">
          <input type="email" id="signupEmail" placeholder="Email" required>
        </div>
        <div class="form-group">
          <input type="tel" id="signupPhone" placeholder="Phone Number" required>
        </div>
        <div class="form-group">
          <input type="password" id="signupPassword" placeholder="Password" required>
        </div>
        <div class="form-group">
          <input type="password" id="confirmPassword" placeholder="Confirm Password" required>
        </div>
        <button type="submit" class="btn-primary full-width">Sign Up</button>
      </form>
      <p class="modal-footer">Already have an account? <a href="#" id="switchToLogin">Login here</a></p>
    </div>
  </div>

  <!-- Cart Modal -->
  <div id="cartModal" class="modal">
    <div class="modal-content cart-modal">
      <span class="close" id="closeCart">&times;</span>
      <h2>Your Cart</h2>
      <div id="cartItems" class="cart-items">
        <!-- Cart items will be loaded here -->
      </div>
      <div class="cart-summary">
        <div class="cart-total">
          <strong>Total: $<span id="cartTotal">0.00</span></strong>
        </div>
        <button id="checkoutBtn" class="btn-primary full-width">Proceed to Checkout</button>
      </div>
    </div>
  </div>

  <!-- Checkout Modal -->
  <div id="checkoutModal" class="modal">
    <div class="modal-content checkout-modal">
      <span class="close" id="closeCheckout">&times;</span>
      <h2>Checkout</h2>
      <form id="checkoutForm">
        <div class="checkout-section">
          <h3>Delivery Address</h3>
          <div class="form-group">
            <input type="text" id="deliveryStreet" placeholder="Street Address" required>
          </div>
          <div class="form-row">
            <div class="form-group">
              <input type="text" id="deliveryCity" placeholder="City" required>
            </div>
            <div class="form-group">
              <input type="text" id="deliveryState" placeholder="State">
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <input type="text" id="deliveryZip" placeholder="ZIP Code">
            </div>
            <div class="form-group">
              <input type="tel" id="deliveryPhone" placeholder="Phone Number" required>
            </div>
          </div>
        </div>

        <div class="checkout-section">
          <h3>Payment Method</h3>
          <div class="payment-options">
            <label class="payment-option">
              <input type="radio" name="paymentMethod" value="cash" checked>
              <span>Cash on Delivery</span>
            </label>
            <label class="payment-option">
              <input type="radio" name="paymentMethod" value="card">
              <span>Credit/Debit Card</span>
            </label>
            <label class="payment-option">
              <input type="radio" name="paymentMethod" value="online">
              <span>Online Payment</span>
            </label>
          </div>
        </div>

        <div class="checkout-section">
          <h3>Special Instructions</h3>
          <textarea id="orderNotes" placeholder="Any special instructions for your order..." rows="3"></textarea>
        </div>

        <div class="order-summary">
          <h3>Order Summary</h3>
          <div id="checkoutItems"></div>
          <div class="total-amount">
            <strong>Total: $<span id="checkoutTotal">0.00</span></strong>
          </div>
        </div>

        <button type="submit" class="btn-primary full-width">Place Order</button>
      </form>
    </div>
  </div>

  <!-- Notification -->
  <div id="notification" class="notification">
    <span id="notificationMessage"></span>
    <button id="closeNotification">&times;</button>
  </div>

  <!-- Loading Spinner -->
  <div id="loadingSpinner" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <script src="script.js"></script>
</body>
</html>
